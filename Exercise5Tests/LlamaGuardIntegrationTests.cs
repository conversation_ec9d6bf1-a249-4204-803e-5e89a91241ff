using Calculators;
using Calculators.ContentSafety;
using NUnit.Framework;

namespace Exercise5Tests
{
    [Category("Integration")]
    [Category("LlamaGuard")]
    public class LlamaGuardIntegrationTests
    {
        private IContentSafetyService _llamaGuardService;
        private HttpClient _httpClient;

        [SetUp]
        public void Setup()
        {
            _httpClient = new HttpClient();
            _llamaGuardService = new OllamaContentSafetyService(_httpClient);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient?.Dispose();
        }

        [TestCase(10.0, 5.0, 15.0)]
        [TestCase(25.0, 25.0, 50.0)]
        [TestCase(-5.0, 10.0, 5.0)]
        public async Task LlamaGuard_SafetyValidatedAddition_ShouldValidateWithRealService(decimal firstNumber, decimal secondNumber, decimal expectedAnswer)
        {
            var calculator = new SimpleCalculator(_llamaGuardService);
            calculator.Enter(firstNumber);
            
            var result = await calculator.Plus(secondNumber).EqualsAsync();
            
            Assert.That(result, Is.EqualTo(expectedAnswer));
            
            var safetyLog = calculator.GetSafetyLog();
            Assert.That(safetyLog.Count, Is.EqualTo(1));
            Assert.That(safetyLog[0].IsSafe, Is.True);
            
            TestContext.Progress.WriteLine($"Llama Guard validated: '{safetyLog[0].Content}' as {(safetyLog[0].IsSafe ? "SAFE" : "UNSAFE")}");
        }

        [TestCase("The calculation result is 42")]
        [TestCase("Mathematical operation completed successfully")]
        [TestCase("Safe arithmetic calculation")]
        public async Task LlamaGuard_ContentValidation_SafeContent_ShouldReturnSafe(string content)
        {
            var result = await _llamaGuardService.ValidateContentAsync(content);
            
            Assert.That(result.IsSafe, Is.True);
            Assert.That(result.Content, Is.EqualTo(content));
            
            TestContext.Progress.WriteLine($"Llama Guard validated: '{content}' as {(result.IsSafe ? "SAFE" : "UNSAFE")}");
        }

        [TestCaseSource(nameof(LlamaGuardTestData))]
        public async Task LlamaGuard_GeneratedSafetyValidatedOperations_ShouldValidateWithRealService(decimal firstNumber, string operation, decimal secondNumber, decimal expectedAnswer)
        {
            var calculator = new SimpleCalculator(_llamaGuardService);
            calculator.Enter(firstNumber);
            
            decimal result = operation switch
            {
                "+" => await calculator.Plus(secondNumber).EqualsAsync(),
                "-" => await calculator.Minus(secondNumber).EqualsAsync(),
                "*" => await calculator.MultiplyBy(secondNumber).EqualsAsync(),
                "/" => await calculator.DivideBy(secondNumber).EqualsAsync(),
                _ => throw new ArgumentException($"Unknown operation: {operation}")
            };
            
            Assert.That(result, Is.EqualTo(expectedAnswer));
            
            var safetyLog = calculator.GetSafetyLog();
            Assert.That(safetyLog.Count, Is.EqualTo(1));
            Assert.That(safetyLog[0].IsSafe, Is.True);
            
            TestContext.Progress.WriteLine($"Llama Guard validated operation {operation}: '{safetyLog[0].Content}' as {(safetyLog[0].IsSafe ? "SAFE" : "UNSAFE")}");
        }

        [Test]
        public async Task LlamaGuard_ServiceAvailability_ShouldConnectToOllama()
        {
            var testContent = "This is a test message to verify Ollama connectivity";
            
            var result = await _llamaGuardService.ValidateContentAsync(testContent);
            
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Content, Is.EqualTo(testContent));
            
            TestContext.Progress.WriteLine($"Llama Guard service is available and responded: {(result.IsSafe ? "SAFE" : "UNSAFE")}");
        }

        public static object[][] LlamaGuardTestData()
        {
            return new object[][]
            {
                new object[] { 5m, "+", 3m, 8m },
                new object[] { 10m, "-", 4m, 6m },
                new object[] { 3m, "*", 7m, 21m },
                new object[] { 20m, "/", 4m, 5m }
            };
        }
    }
}
