using Calculators;
using Calculators.ContentSafety;
using NUnit.Framework;

namespace Exercise5Tests
{
    public class SafetyIntegrationTests
    {
        private IContentSafetyService _safetyService;
        private HttpClient _httpClient;

        [SetUp]
        public void Setup()
        {
            _httpClient = new HttpClient();
            _safetyService = new NullContentSafetyService();
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient?.Dispose();
        }

        [TestCase(10.0, 5.0, 15.0)]
        [TestCase(25.0, 25.0, 50.0)]
        [TestCase(-5.0, 10.0, 5.0)]
        public async Task SafetyValidatedAddition(decimal firstNumber, decimal secondNumber, decimal expectedAnswer)
        {
            var calculator = new SimpleCalculator(_safetyService);
            calculator.Enter(firstNumber);
            
            var result = await calculator.Plus(secondNumber).EqualsAsync();
            
            Assert.That(result, Is.EqualTo(expectedAnswer));
            
            var safetyLog = calculator.GetSafetyLog();
            Assert.That(safetyLog.Count, Is.EqualTo(1));
            Assert.That(safetyLog[0].IsSafe, Is.True);
        }

        [TestCase("Safe calculation result")]
        [TestCase("The answer is 42")]
        [TestCase("Mathematical operation completed")]
        public async Task ContentValidationTestCases(string content)
        {
            var result = await _safetyService.ValidateContentAsync(content);
            
            Assert.That(result.IsSafe, Is.True);
            Assert.That(result.Content, Is.EqualTo(content));
        }

        [TestCaseSource(nameof(SafetyTestData))]
        public async Task GeneratedSafetyValidatedOperations(decimal firstNumber, string operation, decimal secondNumber, decimal expectedAnswer)
        {
            var calculator = new SimpleCalculator(_safetyService);
            calculator.Enter(firstNumber);
            
            decimal result = operation switch
            {
                "+" => await calculator.Plus(secondNumber).EqualsAsync(),
                "-" => await calculator.Minus(secondNumber).EqualsAsync(),
                "*" => await calculator.MultiplyBy(secondNumber).EqualsAsync(),
                "/" => await calculator.DivideBy(secondNumber).EqualsAsync(),
                _ => throw new ArgumentException($"Unknown operation: {operation}")
            };
            
            Assert.That(result, Is.EqualTo(expectedAnswer));
            
            var safetyLog = calculator.GetSafetyLog();
            Assert.That(safetyLog.Count, Is.EqualTo(1));
            Assert.That(safetyLog[0].IsSafe, Is.True);
        }

        public static object[][] SafetyTestData()
        {
            return new object[][]
            {
                new object[] { 5m, "+", 3m, 8m },
                new object[] { 10m, "-", 4m, 6m },
                new object[] { 3m, "*", 7m, 21m },
                new object[] { 20m, "/", 4m, 5m }
            };
        }
    }
}
