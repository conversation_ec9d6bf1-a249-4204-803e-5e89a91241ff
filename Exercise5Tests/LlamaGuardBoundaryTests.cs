using Calculators;
using Calculators.ContentSafety;
using NUnit.Framework;

namespace Exercise5Tests
{
    [Category("Integration")]
    [Category("LlamaGuard")]
    [Category("Boundary")]
    public class LlamaGuardBoundaryTests
    {
        private IContentSafetyService _llamaGuardService;
        private HttpClient _httpClient;

        [SetUp]
        public void Setup()
        {
            _httpClient = new HttpClient();
            _llamaGuardService = new OllamaContentSafetyService(_httpClient);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient?.Dispose();
        }

        [TestCase("")]
        [TestCase(" ")]
        [TestCase("\t")]
        public async Task LlamaGuard_EmptyOrWhitespaceContent_ShouldHandleGracefully(string content)
        {
            var result = await _llamaGuardService.ValidateContentAsync(content);
            
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Content, Is.EqualTo(content));
            
            TestContext.Progress.WriteLine($"Llama Guard handled empty/whitespace content: '{content}' as {(result.IsSafe ? "SAFE" : "UNSAFE")}");
        }

        [TestCase(999999.99)]
        [TestCase(-999999.99)]
        [TestCase(0.000001)]
        public async Task LlamaGuard_ExtremeNumbers_ShouldValidateCorrectly(decimal extremeNumber)
        {
            var calculator = new SimpleCalculator(_llamaGuardService);
            calculator.Enter(extremeNumber);
            
            var result = await calculator.Plus(1).EqualsAsync();
            
            Assert.That(result, Is.EqualTo(extremeNumber + 1));
            
            var safetyLog = calculator.GetSafetyLog();
            Assert.That(safetyLog.Count, Is.EqualTo(1));
            
            TestContext.Progress.WriteLine($"Llama Guard validated extreme number {extremeNumber}: '{safetyLog[0].Content}' as {(safetyLog[0].IsSafe ? "SAFE" : "UNSAFE")}");
        }

        [TestCase("Content with special chars: !@#$%^&*()")]
        [TestCase("Unicode content: 数学计算结果")]
        [TestCase("Emoji content: The result is 42 🧮✨")]
        public async Task LlamaGuard_SpecialCharacters_ShouldValidateCorrectly(string content)
        {
            var result = await _llamaGuardService.ValidateContentAsync(content);
            
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Content, Is.EqualTo(content));
            
            TestContext.Progress.WriteLine($"Llama Guard validated special chars: '{content}' as {(result.IsSafe ? "SAFE" : "UNSAFE")}");
        }

        [Test]
        public async Task LlamaGuard_ChainedOperations_ShouldValidateAllSteps()
        {
            var calculator = new SimpleCalculator(_llamaGuardService);
            calculator.Enter(10);
            
            var result = await calculator
                .Plus(5)      // 15
                .Minus(3)     // 12
                .MultiplyBy(2) // 24
                .DivideBy(4)  // 6
                .EqualsAsync();
            
            Assert.That(result, Is.EqualTo(6));
            
            var safetyLog = calculator.GetSafetyLog();
            Assert.That(safetyLog.Count, Is.EqualTo(1));
            Assert.That(safetyLog[0].IsSafe, Is.True);
            
            TestContext.Progress.WriteLine($"Chained operations validated: '{safetyLog[0].Content}' as {(safetyLog[0].IsSafe ? "SAFE" : "UNSAFE")}");
        }

        [Test]
        public async Task LlamaGuard_ConcurrentValidations_ShouldHandleCorrectly()
        {
            var tasks = new List<Task<SafetyClassification>>();
            
            for (int i = 0; i < 3; i++)
            {
                var content = $"Concurrent validation test {i + 1}";
                tasks.Add(_llamaGuardService.ValidateContentAsync(content));
            }
            
            var results = await Task.WhenAll(tasks);
            
            Assert.That(results.Length, Is.EqualTo(3));
            
            for (int i = 0; i < results.Length; i++)
            {
                Assert.That(results[i], Is.Not.Null);
                Assert.That(results[i].Content, Is.EqualTo($"Concurrent validation test {i + 1}"));
                TestContext.Progress.WriteLine($"Concurrent validation {i + 1}: {(results[i].IsSafe ? "SAFE" : "UNSAFE")}");
            }
        }
    }
}
