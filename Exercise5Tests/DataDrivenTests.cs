using Calculators;
using NUnit.Framework;

namespace Exercise5Tests
{
    public class DataDrivenTests
    {
        [TestCase(5.0, 7.0, 12.0)]
        [TestCase(2.0, -3.0, -1.0)]
        [TestCase(0.0, 10.0, 10.0)]
        [TestCase(-5.0, 3.0, -2.0)]
        public void TODO_ValuesBasedDataDrivenAddition(decimal firstNumber, decimal secondNumber, decimal expectedAnswer)
        {
            Assert.Fail("TODO: Implement this test method using TestCase data");
        }

        [TestCaseSource(nameof(AdditionTestData))]
        public void TODO_GeneratedDataDrivenAddition(decimal firstNumber, decimal secondNumber, decimal expectedAnswer)
        {
            Assert.Fail("TODO: Implement this test method using TestCaseSource data");
        }

        public static object[][] AdditionTestData()
        {
            return new object[][]
            {
                new object[] { 1m, 1m, 2m },
                new object[] { 1m, 2m, 3m },
                new object[] { -1m, -2m, -3m },
                new object[] { 0m, 0m, 0m }
            };
        }

        [TestCase(10.0, 5.0, 5.0)]
        [TestCase(20.0, 8.0, 12.0)]
        [TestCase(0.0, 3.0, -3.0)]
        [TestCase(-5.0, -2.0, -3.0)]
        public void SubtractionTestCases(decimal firstNumber, decimal secondNumber, decimal expectedAnswer)
        {
            var calculator = new SimpleCalculator();
            calculator.Enter(firstNumber);

            var result = calculator.Minus(secondNumber).Equals();

            Assert.That(result, Is.EqualTo(expectedAnswer));
        }

        [TestCase(3.0, 4.0, 12.0)]
        [TestCase(2.5, 2.0, 5.0)]
        [TestCase(-3.0, 2.0, -6.0)]
        [TestCase(0.0, 5.0, 0.0)]
        public void MultiplicationTestCases(decimal firstNumber, decimal secondNumber, decimal expectedAnswer)
        {
            var calculator = new SimpleCalculator();
            calculator.Enter(firstNumber);

            var result = calculator.MultiplyBy(secondNumber).Equals();

            Assert.That(result, Is.EqualTo(expectedAnswer));
        }

        [TestCase(12.0, 3.0, 4.0)]
        [TestCase(15.0, 5.0, 3.0)]
        [TestCase(-10.0, 2.0, -5.0)]
        [TestCase(7.0, 2.0, 3.5)]
        public void DivisionTestCases(decimal firstNumber, decimal secondNumber, decimal expectedAnswer)
        {
            var calculator = new SimpleCalculator();
            calculator.Enter(firstNumber);

            var result = calculator.DivideBy(secondNumber).Equals();

            Assert.That(result, Is.EqualTo(expectedAnswer));
        }

        [TestCaseSource(nameof(SubtractionTestData))]
        public void GeneratedDataDrivenSubtraction(decimal firstNumber, decimal secondNumber, decimal expectedAnswer)
        {
            var calculator = new SimpleCalculator();
            calculator.Enter(firstNumber);

            var result = calculator.Minus(secondNumber).Equals();

            Assert.That(result, Is.EqualTo(expectedAnswer));
        }

        public static object[][] SubtractionTestData()
        {
            return new object[][]
            {
                new object[] { 10m, 3m, 7m },
                new object[] { 5m, 8m, -3m },
                new object[] { -2m, -5m, 3m }
            };
        }
    }
}
