# Exercise 5: Data-Driven Testing with TestCase and TestCaseSource

## Overview

This exercise introduces Data-Driven Testing (DDT) using NUnit's `[TestCase]` and `[TestCaseSource]` annotations. Instead of writing separate test methods for different input values, you will implement test methods that run multiple times with different data sets.

## Learning Objectives

- Understand Data-Driven Testing concepts
- <PERSON>rn to use `[TestCase]` annotation for inline test data
- <PERSON><PERSON> to use `[TestCaseSource]` annotation for generated test data
- Compare different approaches to parameterized testing
- Understand the benefits and limitations of each approach

## Your Task

You need to implement the failing TODO tests in the `Exercise5Tests/DataDrivenTests.cs` file. There are two failing tests that need to be completed:

### 1. TODO_ValuesBasedDataDrivenAddition Test

This test is already decorated with `[TestCase]` attributes but needs implementation:
- `[TestCase(5.0, 7.0, 12.0)]`
- `[TestCase(2.0, -3.0, -1.0)]`
- `[TestCase(0.0, 10.0, 10.0)]`
- `[TestCase(-5.0, 3.0, -2.0)]`

**Your task:** Replace the `Assert.Fail("TODO: ...")` with proper test implementation that:
1. Creates a new `SimpleCalculator` instance
2. Enters the first number using `calculator.Enter(firstNumber)`
3. Adds the second number using `calculator.Plus(secondNumber)`
4. Gets the result using `calculator.Equals()`
5. Asserts that the result equals the expected answer

### 2. TODO_GeneratedDataDrivenAddition Test

This test uses `[TestCaseSource(nameof(AdditionTestData))]` and the data generator method is already provided.

**Your task:** Replace the `Assert.Fail("TODO: ...")` with the same test implementation pattern as above.

The `AdditionTestData()` method provides these test cases:
- `{ 1m, 1m, 2m }`
- `{ 1m, 2m, 3m }`
- `{ -1m, -2m, -3m }`
- `{ 0m, 0m, 0m }`

## Implementation Steps

1. **Open the test file:** Navigate to `Exercise5Tests/DataDrivenTests.cs`
2. **Find the TODO tests:** Look for methods starting with `TODO_`
3. **Implement the test logic:** Follow the pattern used in the working examples in the same file
4. **Run the tests:** Use `dotnet test` to verify your implementation

## Key Concepts

### TestCase Annotation
The `[TestCase]` attribute allows you to specify test data directly in the attribute:
```csharp
[TestCase(5.0, 7.0, 12.0)]
[TestCase(2.0, -3.0, -1.0)]
public void ValuesBasedDataDrivenAddition(decimal firstNumber, decimal secondNumber, decimal expectedAnswer)
{
    // Your implementation here
}
```

### TestCaseSource Annotation
The `[TestCaseSource]` attribute references a method that generates test data:
```csharp
[TestCaseSource(nameof(AdditionTestData))]
public void GeneratedDataDrivenAddition(decimal firstNumber, decimal secondNumber, decimal expectedAnswer)
{
    // Your implementation here
}

public static object[][] AdditionTestData()
{
    return new object[][]
    {
        new object[] { 1m, 1m, 2m },
        new object[] { 1m, 2m, 3m },
        new object[] { -1m, -2m, -3m },
        new object[] { 0m, 0m, 0m }
    };
}
```

## Testing Your Implementation

### Running the Tests
```bash
dotnet build
dotnet test
```

### Expected Behavior
- Initially, the TODO tests will fail with "TODO: Implement this test method..."
- After implementation, all tests should pass
- The working examples (SubtractionTestCases, MultiplicationTestCases, etc.) provide reference implementations

## Discussion Points

### Advantages of TestCase vs TestCaseSource

**TestCase Advantages:**
- Simple and readable
- Test data is visible directly in the test method
- Good for small, static datasets

**TestCase Limitations:**
1. **Compile-time Constants**: TestCase values must be compile-time constants
2. **Type Limitations**: Limited to simple types (no complex objects)
3. **Hardcoded Values**: Cannot generate or calculate values dynamically
4. **Maintenance**: Adding new test cases requires code changes

**TestCaseSource Advantages:**
- Can generate data dynamically
- Supports complex objects
- Better for large datasets
- Data can be loaded from external sources

**TestCaseSource Limitations:**
- More complex setup
- Test data is separated from the test method
- Harder to see what data is being tested at a glance

## Success Criteria

✅ TODO_ValuesBasedDataDrivenAddition test implemented correctly
✅ TODO_GeneratedDataDrivenAddition test implemented correctly
✅ All tests pass when running `dotnet test`
✅ Understanding of TestCase vs TestCaseSource trade-offs
✅ Ability to create data-driven tests for different scenarios
